<template>
  <div class="pv-device-editor">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="厂商" name="manufacturer">
        <a-input 
          v-model:value="formData.manufacturer" 
          placeholder="请输入厂商名称" 
          :disabled="disabled"
        />
      </a-form-item>
      
      <a-form-item label="产品型号" name="model">
        <a-input 
          v-model:value="formData.model" 
          placeholder="请输入产品型号（可选）" 
          :disabled="disabled"
        />
      </a-form-item>
      
      <a-form-item label="衰减率配置" name="dampCurve">
        <div class="damp-curve-section">
          <a-button 
            type="dashed" 
            @click="showDampCurveEditor = true"
            style="width: 100%; margin-bottom: 12px;"
            :disabled="disabled"
          >
            <template #icon>
              <EditOutlined />
            </template>
            {{ dampCurveData.length > 0 ? '编辑衰减率曲线' : '配置衰减率曲线' }}
          </a-button>
          
          <!-- 衰减率预览 -->
          <div v-if="dampCurveData.length > 0" class="curve-preview">
            <div class="preview-header">
              <span>衰减率曲线</span>
            </div>
            <div class="preview-content">
              <EChartsLineChart 
                :data="dampCurveData" 
                :width="400" 
                :height="120"
                :show-axis="true"
                :show-tooltip="true"
                title="衰减率曲线"
              />
            </div>
          </div>
          
          <div v-else class="no-curve">
            <a-empty 
              :image="Empty.PRESENTED_IMAGE_SIMPLE" 
              description="请配置衰减率曲线"
            />
          </div>
        </div>
      </a-form-item>
    </a-form>
    
    <!-- 衰减率编辑抽屉 -->
    <a-drawer
      title="衰减率时间段编辑"
      :visible="showDampCurveEditor"
      :width="600"
      @close="showDampCurveEditor = false"
      destroy-on-close
    >
      <DampCurveEditor
        v-model="dampCurveData"
        :max-years="25"
        @change="handleDampCurveChange"
      />
      
      <template #footer>
        <div class="drawer-footer">
          <a-button @click="showDampCurveEditor = false">取消</a-button>
          <a-button 
            type="primary" 
            @click="confirmDampCurve"
            :disabled="dampCurveData.length === 0"
          >
            确认
          </a-button>
        </div>
      </template>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits, defineExpose } from 'vue'
import { message, Empty } from 'ant-design-vue'
import { EditOutlined } from '@ant-design/icons-vue'
import DampCurveEditor from '@/components/DampCurveEditor/index.vue'
import EChartsLineChart from '@/components/EChartsLineChart/index.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      manufacturer: '',
      model: '',
      dampCurve: []
    })
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref()
const showDampCurveEditor = ref(false)

// 表单数据
const formData = ref({
  manufacturer: '',
  model: '',
  dampCurve: []
})

// 衰减率曲线数据
const dampCurveData = ref([])

// 表单验证规则
const rules = {
  manufacturer: [
    { required: true, message: '请输入厂商名称', trigger: 'blur' }
  ],
  model: [
    // 产品型号为可选字段
  ],
  dampCurve: [
    { 
      validator: (rule, value) => {
        if (!dampCurveData.value || dampCurveData.value.length === 0) {
          return Promise.reject('请配置衰减率曲线')
        }
        return Promise.resolve()
      },
      trigger: 'change'
    }
  ]
}

// 处理衰减率曲线变化
const handleDampCurveChange = (data) => {
  dampCurveData.value = data
  formData.value.dampCurve = data
  emitChange()
}

// 确认衰减率配置
const confirmDampCurve = () => {
  if (dampCurveData.value.length === 0) {
    message.warning('请配置正确的衰减率时间段')
    return
  }
  
  showDampCurveEditor.value = false
  message.success('衰减率曲线配置成功')
  
  // 触发表单验证
  formRef.value?.validateFields(['dampCurve'])
}

// 发送数据变化事件
const emitChange = () => {
  const data = {
    manufacturer: formData.value.manufacturer,
    model: formData.value.model,
    dampCurve: dampCurveData.value
  }
  emit('update:modelValue', data)
  emit('change', data)
}

// 监听表单数据变化
watch(() => formData.value.manufacturer, emitChange)
watch(() => formData.value.model, emitChange)

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    formData.value.manufacturer = newValue.manufacturer || ''
    formData.value.model = newValue.model || ''
    if (newValue.dampCurve && newValue.dampCurve.length > 0) {
      dampCurveData.value = [...newValue.dampCurve]
      formData.value.dampCurve = [...newValue.dampCurve]
    }
  }
}, { immediate: true, deep: true })

// 验证表单
const validate = async () => {
  try {
    await formRef.value.validateFields()
    return true
  } catch (error) {
    console.log('表单验证失败:', error)
    return false
  }
}

// 重置表单
const resetFields = () => {
  formData.value = {
    manufacturer: '',
    model: '',
    dampCurve: []
  }
  dampCurveData.value = []
  formRef.value?.resetFields()
}

// 获取表单数据
const getFormData = () => {
  return {
    type: 1, // 光伏设备类型
    baseInfo: {
      manufacturer: formData.value.manufacturer,
      model: formData.value.model
    },
    params: {
      damp_curve: {
        data: dampCurveData.value
      }
    }
  }
}

// 暴露方法
defineExpose({
  validate,
  resetFields,
  getFormData
})
</script>

<style scoped>
.pv-device-editor {
  padding: 0;
}

.damp-curve-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.curve-preview {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  background: white;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}

.preview-content {
  text-align: center;
}

.no-curve {
  text-align: center;
  padding: 20px;
}

.drawer-footer {
  text-align: right;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  background: #fff;
}

.drawer-footer .ant-btn {
  margin-left: 8px;
}
</style>
