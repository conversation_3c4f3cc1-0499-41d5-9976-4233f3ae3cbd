<template>
  <div class="body_wrap">
    <div class="p_wrap">
      <div class="title_wrap">
        <!-- <a-breadcrumb>
          <a-breadcrumb-item>返回</a-breadcrumb-item>
          <a-breadcrumb-item><a href="">项目名称</a></a-breadcrumb-item>
        </a-breadcrumb> -->
        <!-- <div class="btn_wrap">
          <a-button class="btn_item" size="small"  type="primary" @click="createNewPro">新建项目</a-button>
        </div> -->
      </div>
      <div class="content_wrap" id="content_wrap">
        <div class="part_wrap">
          <div class="p_title">
            <div>{{ $t('projectList.title') }}</div>
            <div class="btn_wrap">
              <a-button class="btn_item" size2="small"  type="primary" @click="createNewPro">{{ $t('projectList.buttons.newProject') }}</a-button>
            </div>
          </div>
          <div class="table_wrap">
            <div>
              <a-input-search
                v-model:value="searchForm.searchWord"
                :placeholder="$t('projectList.search.placeholder')"
                style="width: 230px;margin-bottom: 15px;"
                @search="getTableData"
              />
            </div>
            <a-table
              borderd
              class="outer_table ant-table-striped"
               :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
              :loading="loading"
              :columns="capColumns()" rowKey="id" :data-source="tableData"
              :defaultExpandAllRows="defaultExpandAllRows"
              @change="pageChange"
              :pagination="{
                pageSize: searchForm.pageSize,
                total: total,
                hideOnSinglePage: false,
                showTotal: total => $t('projectList.pagination.total', { total })
              }"
              >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'action'">
                  <div class="t_btn_wrap">
                    <a href="void:0" class="a_item" @click="createSolution(record)">{{ $t('projectList.buttons.newSolution') }}</a>
                    <a href="void:0" class="a_item" @click="toEditProject(record)">{{ $t('projectList.buttons.editProject') }}</a>
                  </div>
                  
                </template>
                <template  v-if="column.key==='solution'">
                  <a-table class="inner_table" :showHeader="true"  :columns="innerColumns()" :data-source="record.solutions" :pagination="false" size="small" :defaultExpandAllRows="true">
                    <template #bodyCell="{ column, text, record: r }">
                      <template v-if="column.key === 'action'">
                        <div class="t_btn_wrap">
                          <a  class="a_item" @click="router.push({
                            name: 'projectDetail',
                            query: {
                              projectId: record.id,
                              solutionId: r.id,
                              type: 'list'
                            }
                          })">{{ $t('projectList.buttons.view') }}</a>
                          <a href="void:0" class="a_item" @click="createSolution(record, r)">{{ $t('projectList.buttons.editSolution') }}</a>
                          <a href="void:0" class="a_item" @click="toEconomicAnalysis(record, r)">{{ $t('projectList.buttons.economicAnalysis') }}</a>
                        </div>
                      </template>
                      <template v-if="column.key === 'createTime'">
                        {{ formatDate(text) }}
                      </template>
                      <template v-if="column.key === 'status'">
                        <a-tag :color="getOptionByValue(projectStatus(), text)?.color">{{ getOptionByValue(projectStatus(), text)?.label  }}</a-tag>
                      </template>
                      <template v-if="column.key === 'topology'">
                        <a-tag v-for="item in createTopoTagList(text)">{{ item.label }}</a-tag>
                      </template>
                      <template v-if="column.key === 'targetExpr'">
                        <a-tag v-for="item in createTargetTagList(text)">{{ `${item.label}: ${item.value * 100}%` }}</a-tag>
                      </template>
                    </template>
                  </a-table>
                </template>
              </template>
              <!-- <template v-slot:expandedRowRender="{record, index, indent, expanded}">
                <a-table bordered :columns="innerColumns()" :data-source="record.solutions" :pagination="false" size="small" :defaultExpandAllRows="true">
                  <template #bodyCell="{ column, text, record: r }">
                    <template v-if="column.key === 'action'">
                      <div class="t_btn_wrap">
                        <a href="void:0" class="a_item" @click="router.push({
                          name: 'projectDetail',
                          query: {
                            projectId: record.id,
                            solutionId: r.id
                          }
                        })">查看</a>
                        <a href="void:0" class="a_item" @click="createSolution(record, r)">修改方案</a>
                        <a href="void:0" class="a_item" disabled>经济分析</a>
                      </div>
                     </template>
                    <template v-if="column.key === 'createTime'">
                      {{ formatDate(text) }}
                    </template>
                    <template v-if="column.key === 'status'">
                      <a-tag :color="getOptionByValue(projectStatus(), text)?.color">{{ getOptionByValue(projectStatus(), text)?.label  }}</a-tag>
                    </template>
                    <template v-if="column.key === 'topology'">
                      
                    </template>
                  </template>
                </a-table>
              </template> -->
            </a-table>
          </div>
        </div>
      </div>
    </div>

    <a-modal
      v-model:open="visiableEditProjectModal" :title="$t('projectList.editProjectModal.title')"
      @ok="submitEditProject"
      :confirm-loading="confirmLoading"
      >
      <div>
        <a-form
          labelAlign="left2"
          ref="formRef"
          :model="formState"
          name="basic"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
          autocomplete="off"
        >
          <a-form-item
            :label="$t('projectList.editProjectModal.projectName')"
            name="name"
            :rules="[{ required: true, message: $t('projectList.editProjectModal.projectNameRequired') }]"
          >
            <a-input v-model:value="formState.name" />
          </a-form-item>
          <a-form-item
            :label="$t('projectList.editProjectModal.customerName')"
            name="customer"
            :rules="[{ required: true, message: $t('projectList.editProjectModal.customerNameRequired') }]"
          >
            <a-input v-model:value="formState.customer" />
          </a-form-item>
          <a-form-item
            :label="$t('projectList.editProjectModal.projectBackground')"
            name="desc"
            :rules="[{ required: true, message: $t('projectList.editProjectModal.projectBackgroundRequired') }]"
          >
            <a-textarea v-model:value="formState.desc" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { getProjects, modifyProject } from '@/api/project'
import { useRouter } from 'vue-router'
import {  capColumns, innerColumns, projectStatus, topo, target } from './util'
import LineChart from '@/components/LineChart/index.vue'
import { formatDate, getOptionByValue } from '@/util'
import { cloneDeep } from 'lodash'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()
const loading = ref(false)
const visiableEditProjectModal = ref(false)
const formRef = ref()
const tableData = ref([])
const formState = ref({})
const curProjectItem = ref({})
const confirmLoading = ref(false)
const defaultExpandAllRows = ref(false)
const total = ref(0)
const searchForm = ref({
  operator: '',
  pageSize: 10,
  pageNumber: 1,
  searchWord: ''
})


const createTargetTagList = (targetExpr) => {
  const tResult = []
  const tList = target()
  for (let i = 0, len = tList.length; i < len; i++) {
    if (targetExpr?.[i] !== 0) {
      tResult.push({
        label: tList[i],
        value: targetExpr?.[i]
      })
    }
  }
  return tResult
}

const createTopoTagList = (topology) => {
  const tResult = []
  const colors = ['#87d068', '#87d068', ]
  const tList = topo()
  for (let i = 0, len = tList.length; i < len; i++) {
    if (topology?.[i]) {
      tResult.push({
        label: tList[i],
      })
    }
  }
  return tResult
}

const createNewPro = () => {
  router.push({ name: 'createProject' })
}

const createSolution = (projectItem, solution) => {
  console.log('ceateSolution:', projectItem, solution)
  // TODO: 新建方案，不需要项目 id
  const query = {
    projectId: projectItem.id
  }
  if (solution?.id !== undefined) {
    query.solutionId = solution.id
  }
  router.push({ name: 'createProject', query})
}
const toEditProject = (record) => {
  visiableEditProjectModal.value = true
  curProjectItem.value = record
  formState.value = cloneDeep(record)
}
const submitEditProject = async () =>{
  const values = await formRef.value.validateFields()
  confirmLoading.value = true
  const { code, msg } = await modifyProject({
    id: curProjectItem.value.id,
    ...values
  })
  confirmLoading.value = false
  if (code ===0 ) {
    message.success(t('projectList.editProjectModal.editSuccess'))
    initData()
  } else {
    message.error(msg)
  }
  visiableEditProjectModal.value = false
}

const toEconomicAnalysis = (projectItem, solution) => {
  router.push({
    name: 'economicAnalysisCreate',
    query: {
      capProjectId: projectItem.id,
      capSolutionId: solution.id,
    }
  })
}
const getTableData = async (hasLoading = true) => {
  loading.value = hasLoading
  const reqParams = {
    pageSize: searchForm.value.pageSize,
    pageNumber: searchForm.value.pageNumber,
  }
  if (searchForm.value.searchWord) {
    reqParams.search = searchForm.value.searchWord
  }
  if (searchForm.value.operator?.trim()) {
    reqParams.operator = searchForm.value.operator
  }
  const { msg, data, code } = await getProjects(reqParams)
  console.log('code:', code, data)
  loading.value = false
  if (code === 0) {
    tableData.value = data.result
    total.value = data.total
  }
}

const pageChange = (page) => {
  console.log('page:', page)
  searchForm.value.pageNumber = page.current
  searchForm.value.pageSize = page.pageSize
  getTableData()
}

const initData = async () => {
  getTableData()
}

onMounted(() => {
  initData()
  setTimeout(() => {
    defaultExpandAllRows.value = true

  }, 5000)
})
</script>

<style lang="less" scoped>
@import '@/style/base.less';

.body_wrap {
  font-size: 12px;
  padding: 10px 20px;
  position: relative;
  // * {
  //   font-size: 12px;
  // }
}
.p_wrap {
  .title_wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .btn_wrap {
      .btn_item {
        margin-left: 10px;
      }
    }
  }
  
}
.content_wrap {
  margin-top: 20px;
  .p_title {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    font-weight: bold;
    // margin-bottom: 15px;
  }
  .table_wrap {
    .t_btn_wrap {
      .a_item {
        margin-right: 8px;
        font-size: 12px;
      }
    }
  }

}
.inner_table {
  :deep(th.ant-table-cell) {
    background: none;
    // font-weight: 500;
    text-align: left!important;

  }
}
.outer_table{
  :deep(th.ant-table-cell:nth-child(5)) {
    text-align: center;
  }
}
// .ant-table-striped :deep(.table-striped) td {
//   background-color: #fafafa;
// }

</style>
