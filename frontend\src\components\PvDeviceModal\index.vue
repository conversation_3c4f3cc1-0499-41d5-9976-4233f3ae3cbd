<template>
  <a-modal
    :visible="visible"
    :title="isEditing ? '修改光伏设备' : '新增光伏设备'"
    :width="800"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :confirm-loading="confirmLoading"
    destroy-on-close
  >
    <PvDeviceEditor
      ref="editorRef"
      v-model="deviceData"
      @change="handleDeviceChange"
    />
  </a-modal>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'
import PvDeviceEditor from '@/components/PvDeviceEditor/index.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => ({})
  },
  confirmLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

const editorRef = ref()

// 设备数据
const deviceData = ref({
  manufacturer: '',
  model: '',
  dampCurve: []
})

// 处理设备数据变化
const handleDeviceChange = (data) => {
  deviceData.value = data
}

// 确认提交
const handleConfirm = async () => {
  const isValid = await editorRef.value?.validate()
  if (!isValid) return

  const formData = editorRef.value?.getFormData()

  if (props.isEditing && props.editData.id) {
    formData.baseInfo.id = props.editData.id
  }

  emit('confirm', formData)
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}

// 初始化编辑数据
const initEditData = () => {
  if (props.isEditing && props.editData) {
    const data = {
      manufacturer: props.editData.manufacturer || '',
      model: props.editData.model || '',
      dampCurve: props.editData.damp_curve?.data || []
    }
    deviceData.value = data
  }
}

// 重置表单
const resetForm = () => {
  deviceData.value = {
    manufacturer: '',
    model: '',
    dampCurve: []
  }
  editorRef.value?.resetFields()
}

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    if (props.isEditing) {
      initEditData()
    } else {
      resetForm()
    }
  }
})

// 监听编辑数据变化
watch(() => props.editData, () => {
  if (props.visible && props.isEditing) {
    initEditData()
  }
}, { deep: true })
</script>

<style scoped>
/* 样式已移至 PvDeviceEditor 组件 */
</style>
