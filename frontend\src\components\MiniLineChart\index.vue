<template>
  <div class="mini-line-chart" :style="{ width: width + 'px', height: height + 'px' }">
    <svg :width="width" :height="height" :viewBox="`0 0 ${width} ${height}`">
      <!-- 背景网格线 -->
      <defs>
        <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
          <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#f0f0f0" stroke-width="0.5"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#grid)" />
      
      <!-- 折线 -->
      <polyline
        :points="pathPoints"
        fill="none"
        stroke="#1890ff"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      
      <!-- 数据点 -->
      <circle
        v-for="(point, index) in points"
        :key="index"
        :cx="point.x"
        :cy="point.y"
        r="2"
        fill="#1890ff"
      />
    </svg>
  </div>
</template>

<script setup>
import { computed, defineProps } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
    required: true
  },
  width: {
    type: Number,
    default: 120
  },
  height: {
    type: Number,
    default: 40
  },
  padding: {
    type: Number,
    default: 4
  }
})

// 计算数据点坐标
const points = computed(() => {
  if (!props.data || props.data.length === 0) return []
  
  const { data, width, height, padding } = props
  const chartWidth = width - padding * 2
  const chartHeight = height - padding * 2
  
  const maxValue = Math.max(...data)
  const minValue = Math.min(...data)
  const valueRange = maxValue - minValue || 1 // 避免除零
  
  return data.map((value, index) => {
    const x = padding + (index / (data.length - 1)) * chartWidth
    const y = padding + ((maxValue - value) / valueRange) * chartHeight
    return { x, y }
  })
})

// 计算路径点字符串
const pathPoints = computed(() => {
  return points.value.map(point => `${point.x},${point.y}`).join(' ')
})
</script>

<style scoped>
.mini-line-chart {
  display: inline-block;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fafafa;
}

.mini-line-chart svg {
  display: block;
}
</style>
