<template>
  <div class="damp-curve-editor">
    <div class="editor-header">
      <h4>衰减率时间段编辑</h4>
      <a-button 
        type="primary" 
        size="small" 
        @click="addPeriod"
        :disabled="!canAddPeriod"
      >
        新增时间段
      </a-button>
    </div>
    
    <div class="periods-list">
      <div 
        v-for="(period, index) in periods" 
        :key="index"
        class="period-item"
      >
        <a-card size="small">
          <template #title>
            <div class="period-title">
              <span>时段 {{ index + 1 }}</span>
              <a-button 
                size="small" 
                danger 
                type="text"
                @click="removePeriod(index)"
                :disabled="periods.length <= 1"
              >
                删除
              </a-button>
            </div>
          </template>
          
          <div class="period-content">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item
                  label="开始年份"
                  :validate-status="period.startYearError ? 'error' : ''"
                  :help="period.startYearError"
                >
                  <a-input-number
                    v-model:value="period.startYear"
                    :min="1"
                    :max="maxYears"
                    @change="handleInputChange"
                    placeholder="开始年份"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="结束年份"
                  :validate-status="period.endYearError ? 'error' : ''"
                  :help="period.endYearError"
                >
                  <a-input-number
                    v-model:value="period.endYear"
                    :min="1"
                    :max="maxYears"
                    @change="handleInputChange"
                    placeholder="结束年份"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="衰减率"
                  :validate-status="period.dampRateError ? 'error' : ''"
                  :help="period.dampRateError"
                >
                  <a-input-number
                    v-model:value="period.dampRate"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    @change="handleInputChange"
                    placeholder="衰减率"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 时间段整体错误提示 -->
            <div v-if="period.error" class="period-error-message">
              <a-alert
                type="error"
                :message="period.error"
                show-icon
                size="small"
              />
            </div>
          </div>
        </a-card>
      </div>
    </div>
    
    <!-- 校验错误提示 -->
    <div class="validation-summary" v-if="validationErrors.length > 0">
      <a-alert
        type="error"
        :message="`时间段配置错误 (${validationErrors.length}个)`"
        show-icon
        style="margin-bottom: 16px;"
      >
        <template #description>
          <ul class="error-list">
            <li v-for="(error, index) in validationErrors" :key="index">
              {{ error }}
            </li>
          </ul>
        </template>
      </a-alert>
    </div>

    <!-- 配置提示 -->
    <div class="config-tips" v-if="validationErrors.length === 0 && periods.length > 0">
      <a-alert
        type="success"
        message="时间段配置正确"
        :description="`已配置${periods.length}个时间段，覆盖${props.maxYears}年完整周期`"
        show-icon
        style="margin-bottom: 16px;"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  maxYears: {
    type: Number,
    default: 25
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 时间段数据
const periods = ref([
  {
    startYear: 1,
    endYear: 25,
    dampRate: 0.95,
    error: null,
    startYearError: null,
    endYearError: null,
    dampRateError: null
  }
])

// 验证错误列表
const validationErrors = ref([])

// 是否可以添加新时间段
const canAddPeriod = computed(() => {
  if (periods.value.length === 0) return true
  const lastPeriod = periods.value[periods.value.length - 1]
  return lastPeriod.endYear < props.maxYears
})

// 计算衰减率曲线数据
const dampCurveData = computed(() => {
  if (validationErrors.value.length > 0) return []
  
  const data = new Array(props.maxYears).fill(0)
  
  periods.value.forEach(period => {
    if (period.startYear && period.endYear && period.dampRate !== null) {
      for (let year = period.startYear; year <= period.endYear; year++) {
        if (year <= props.maxYears) {
          data[year - 1] = period.dampRate
        }
      }
    }
  })
  
  return data
})

// 处理输入变化
const handleInputChange = () => {
  validatePeriods()
}

// 添加时间段
const addPeriod = () => {
  if (!canAddPeriod.value) return
  
  const lastPeriod = periods.value[periods.value.length - 1]
  const startYear = lastPeriod ? lastPeriod.endYear + 1 : 1
  
  periods.value.push({
    startYear,
    endYear: props.maxYears,
    dampRate: 0.95,
    error: null,
    startYearError: null,
    endYearError: null,
    dampRateError: null
  })
  
  validatePeriods()
}

// 删除时间段
const removePeriod = (index) => {
  if (periods.value.length <= 1) {
    message.warning('至少需要保留一个时间段')
    return
  }
  
  periods.value.splice(index, 1)
  validatePeriods()
}

// 验证时间段
const validatePeriods = () => {
  validationErrors.value = []

  // 清除之前的错误
  periods.value.forEach(period => {
    period.error = null
    period.startYearError = null
    period.endYearError = null
    period.dampRateError = null
  })

  // 检查每个时间段的有效性
  periods.value.forEach((period, index) => {
    let hasError = false

    // 验证开始年份
    if (!period.startYear) {
      period.startYearError = '请输入开始年份'
      hasError = true
    } else if (period.startYear < 1) {
      period.startYearError = '开始年份不能小于1'
      hasError = true
    } else if (period.startYear > props.maxYears) {
      period.startYearError = `开始年份不能大于${props.maxYears}`
      hasError = true
    }

    // 验证结束年份
    if (!period.endYear) {
      period.endYearError = '请输入结束年份'
      hasError = true
    } else if (period.endYear > props.maxYears) {
      period.endYearError = `结束年份不能大于${props.maxYears}`
      hasError = true
    } else if (period.endYear < 1) {
      period.endYearError = '结束年份不能小于1'
      hasError = true
    }

    // 验证衰减率
    if (period.dampRate === null || period.dampRate === undefined) {
      period.dampRateError = '请输入衰减率'
      hasError = true
    } else if (period.dampRate < 0) {
      period.dampRateError = '衰减率不能小于0'
      hasError = true
    } else if (period.dampRate > 1) {
      period.dampRateError = '衰减率不能大于1'
      hasError = true
    }

    // 验证年份范围关系
    if (period.startYear && period.endYear && period.startYear > period.endYear) {
      period.endYearError = '结束年份不能小于开始年份'
      hasError = true
    }

    if (hasError) {
      validationErrors.value.push(`时段${index + 1}存在错误`)
    }
  })
  
  // 检查时间段是否连续、完整、无重复（仅在单个时间段验证通过后进行）
  const hasIndividualErrors = periods.value.some(period =>
    period.startYearError || period.endYearError || period.dampRateError
  )

  if (!hasIndividualErrors && periods.value.length > 0) {
    const sortedPeriods = [...periods.value].sort((a, b) => a.startYear - b.startYear)

    // 检查是否从第1年开始
    if (sortedPeriods[0].startYear !== 1) {
      validationErrors.value.push('⚠️ 时间段必须从第1年开始')
      sortedPeriods[0].error = '此时间段应从第1年开始'
    }

    // 检查是否到最后一年结束
    if (sortedPeriods[sortedPeriods.length - 1].endYear !== props.maxYears) {
      validationErrors.value.push(`⚠️ 时间段必须到第${props.maxYears}年结束`)
      sortedPeriods[sortedPeriods.length - 1].error = `此时间段应到第${props.maxYears}年结束`
    }

    // 检查连续性和重复
    for (let i = 0; i < sortedPeriods.length - 1; i++) {
      const current = sortedPeriods[i]
      const next = sortedPeriods[i + 1]

      if (current.endYear + 1 !== next.startYear) {
        if (current.endYear >= next.startYear) {
          validationErrors.value.push(`❌ 时段${i + 1}(第${current.startYear}-${current.endYear}年)和时段${i + 2}(第${next.startYear}-${next.endYear}年)存在重叠`)
          current.error = `与下一时间段重叠`
          next.error = `与上一时间段重叠`
        } else {
          const gapStart = current.endYear + 1
          const gapEnd = next.startYear - 1
          validationErrors.value.push(`⚠️ 时段${i + 1}和时段${i + 2}之间存在间隔(第${gapStart}-${gapEnd}年未覆盖)`)
          current.error = `与下一时间段存在间隔`
          next.error = `与上一时间段存在间隔`
        }
      }
    }

    // 检查是否有重复的年份范围
    const yearCoverage = new Array(props.maxYears).fill(0)
    periods.value.forEach((period) => {
      if (period.startYear && period.endYear) {
        for (let year = period.startYear; year <= period.endYear; year++) {
          if (year >= 1 && year <= props.maxYears) {
            yearCoverage[year - 1]++
            if (yearCoverage[year - 1] > 1) {
              validationErrors.value.push(`❌ 第${year}年被多个时间段覆盖`)
              period.error = `存在年份重复覆盖`
            }
          }
        }
      }
    })
  }
  
  // 发送数据更新
  if (validationErrors.value.length === 0) {
    // 创建新数组确保响应式更新
    const newData = [...dampCurveData.value]
    emit('update:modelValue', newData)
    emit('change', newData)
  } else {
    emit('update:modelValue', [])
    emit('change', [])
  }
}

// 初始化数据
const initializeFromData = (data) => {
  if (!data || data.length === 0) return
  
  // 从数组数据反推时间段配置
  // 这里简化处理，假设相同值的连续年份为一个时间段
  const newPeriods = []
  let currentValue = data[0]
  let startYear = 1
  
  for (let i = 1; i <= data.length; i++) {
    if (i === data.length || data[i] !== currentValue) {
      newPeriods.push({
        startYear,
        endYear: i,
        dampRate: currentValue,
        error: null,
        startYearError: null,
        endYearError: null,
        dampRateError: null
      })
      
      if (i < data.length) {
        currentValue = data[i]
        startYear = i + 1
      }
    }
  }
  
  if (newPeriods.length > 0) {
    periods.value = newPeriods
    validatePeriods()
  }
}

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue.length > 0) {
    initializeFromData(newValue)
  }
}, { immediate: true })

// 初始验证
validatePeriods()
</script>

<style scoped>
.damp-curve-editor {
  padding: 16px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.editor-header h4 {
  margin: 0;
}

.periods-list {
  margin-bottom: 16px;
}

.period-item {
  margin-bottom: 12px;
}

.period-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.period-content {
  margin-top: 8px;
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.validation-summary {
  margin-bottom: 16px;
}

.preview-section h5 {
  margin-bottom: 8px;
}

.curve-preview {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}

.year-labels {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.year-label {
  font-size: 12px;
  padding: 2px 6px;
  background: #e6f7ff;
  border-radius: 2px;
  border: 1px solid #91d5ff;
}

.no-preview {
  text-align: center;
  color: #999;
  padding: 20px;
}

.period-error-message {
  margin-top: 8px;
}

.error-list {
  margin: 0;
  padding-left: 16px;
}

.error-list li {
  margin-bottom: 4px;
  color: #ff4d4f;
}

.validation-summary {
  margin-bottom: 16px;
}

.config-tips {
  margin-bottom: 16px;
}
</style>
