<template>
  <div 
    ref="chartRef" 
    class="echarts-line-chart" 
    :style="{ width: width + 'px', height: height + 'px' }"
  ></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, defineProps, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
    required: true
  },
  width: {
    type: Number,
    default: 300
  },
  height: {
    type: Number,
    default: 120
  },
  showAxis: {
    type: Boolean,
    default: true
  },
  showTooltip: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: ''
  }
})

const chartRef = ref()
let chartInstance = null

// 初始化图表
const initChart = () => {
  if (!chartRef.value || !props.data || props.data.length === 0) return
  
  // 销毁已存在的实例
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    title: {
      text: props.title,
      textStyle: {
        fontSize: 12,
        color: '#666'
      },
      left: 'center',
      top: 5
    },
    grid: {
      left: props.showAxis ? 30 : 10,
      right: 10,
      top: props.title ? 30 : 10,
      bottom: props.showAxis ? 25 : 10,
      containLabel: false
    },
    xAxis: {
      type: 'category',
      data: props.data.map((_, index) => `第${index + 1}年`),
      show: props.showAxis,
      axisLabel: {
        fontSize: 10,
        color: '#666',
        interval: Math.floor(props.data.length / 5) // 控制标签显示间隔
      },
      axisLine: {
        lineStyle: {
          color: '#d9d9d9'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      show: props.showAxis,
      axisLabel: {
        fontSize: 10,
        color: '#666',
        formatter: '{value}'
      },
      axisLine: {
        lineStyle: {
          color: '#d9d9d9'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    tooltip: {
      show: props.showTooltip,
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: function(params) {
        const dataIndex = params[0].dataIndex
        const value = props.data[dataIndex]
        return `第${dataIndex + 1}年<br/>衰减率: ${(value * 100).toFixed(2)}%`
      }
    },
    series: [
      {
        type: 'line',
        data: props.data,
        smooth: true,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: {
          color: '#1890ff',
          width: 2
        },
        itemStyle: {
          color: '#1890ff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(24, 144, 255, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(24, 144, 255, 0.05)'
              }
            ]
          }
        }
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance || !props.data || props.data.length === 0) return
  
  const option = {
    xAxis: {
      data: props.data.map((_, index) => `第${index + 1}年`)
    },
    series: [
      {
        data: props.data
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    if (props.data && props.data.length > 0) {
      if (chartInstance) {
        updateChart()
      } else {
        initChart()
      }
    }
  })
}, { deep: true })

// 监听尺寸变化
watch([() => props.width, () => props.height], () => {
  nextTick(() => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
})

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

// 暴露方法供外部调用
defineExpose({
  resize: () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }
})
</script>

<style scoped>
.echarts-line-chart {
  display: inline-block;
}
</style>
